import requests
import time
from typing import List, Dict, Any, Optional

def get_pod_info(container_keyword: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    获取pod信息，支持 container 或 namespace 字段模糊匹配。
    :param container_keyword: 需要模糊匹配的container关键字，如果为None则返回全量数据。
    :return: 包含src, region, pod, namespace, env, container字段的pod信息列表。
    """
    # 计算当前时间的前1小时和当前时间的时间戳
    end = int(time.time())
    start = end - 3600

    url = 'https://cnp-proxy.gwm.cn/grafana/api/datasources/12/resources/api/v1/series'
    data = {
        'match[]': 'kube_pod_container_info{}',
        'start': str(start),
        'end': str(end)
    }
    try:
        response = requests.post(url, data=data, timeout=10)
        response.raise_for_status()
        result = response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return []

    if result.get('status') != 'success' or 'data' not in result:
        return []

    pods = result['data']
    filtered = []
    for pod in pods:
        if container_keyword:
            # container和namespace字段都要模糊匹配
            if container_keyword not in pod.get('container', '') and container_keyword not in pod.get('namespace', ''):
                continue
        filtered.append({
            'src': pod.get('src'),
            'region': pod.get('region'),
            'pod': pod.get('pod'),
            'namespace': pod.get('namespace'),
            'env': pod.get('env_zh'),
            'container': pod.get('container'),
        })
    return filtered

if __name__ == "__main__":
    # 示例：模糊匹配container字段
    result = get_pod_info("20250604152004")
    print(result)
    # 示例：获取全量数据
    # all_data = get_pod_info()
    # print(all_data)
