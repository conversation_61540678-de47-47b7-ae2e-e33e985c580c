{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "declaration": true, "sourceMap": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.js"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}