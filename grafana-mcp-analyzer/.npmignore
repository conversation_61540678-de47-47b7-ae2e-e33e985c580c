# Source files (only include built dist/)
src/
tsconfig.json

# Development files
.env
.env.*
*.log
*.tmp
.DS_Store
Thumbs.db

# Test files
tests/
test/
*.test.*
*.spec.*
coverage/

# Documentation (keep essential docs only)
docs/
*.md
!README.md
!CHANGELOG.md

# Development and publish files
PUBLISH_CHECKLIST.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Git files
.git/
.gitignore

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Development scripts and configs
setup-env.sh
start-mcp.sh
cursor-mcp-config.json

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime files
*.pid
*.seed
*.pid.lock

# Build artifacts (keep dist/)
build/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db 