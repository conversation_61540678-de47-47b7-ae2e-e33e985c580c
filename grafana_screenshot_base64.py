import base64
import time
import logging
from playwright.async_api import async_playwright
import asyncio

# 配置日志
logger = logging.getLogger(__name__)

async def get_grafana_processes_screenshot_base64(url: str) -> str:
    """
    访问指定的Grafana URL，按照 screenshot_grafana.py 的完整逻辑，展开所有面板、滚动到底部，
    截取全页面截图，并返回base64编码的PNG图片数据。
    """
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        # 设置初始视口大小
        viewport_size = {"width": 1920, "height": 5000}
        await page.set_viewport_size(viewport_size)
        await page.goto(url)
        # 等待页面基本加载完成
        await page.wait_for_load_state("networkidle")
        await page.wait_for_timeout(5000)
        # 展开所有面板
        toggle_buttons = await page.query_selector_all("div[data-testid*='dashboard-row'] button")
        if toggle_buttons:
            for i, button in enumerate(toggle_buttons):
                if i == 0:
                    continue
                try:
                    await button.scroll_into_view_if_needed()
                    await button.wait_for_element_state("visible")
                    await button.click()
                    await page.wait_for_load_state("networkidle")
                    await page.wait_for_timeout(3000)
                except Exception:
                    pass
        # 滚动到底部，确保所有内容加载
        previous_height = 0
        while True:
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(2000)
            current_height = await page.evaluate("document.body.scrollHeight")
            if current_height == previous_height:
                break
            previous_height = current_height
        await page.wait_for_timeout(10000)
        # 获取页面总高度并设置视口
        total_height = await page.evaluate("document.body.scrollHeight")
        await page.set_viewport_size({"width": 1920, "height": total_height})
        await page.evaluate("window.scrollTo(0, 0)")
        # 截图并获取二进制数据
        img_bytes = await page.screenshot(full_page=True)
        await browser.close()
        # 转为base64
        base64_str = base64.b64encode(img_bytes).decode("utf-8")
        return base64_str


async def capture_full_page_screenshot_base64(url):
    """
    启动浏览器，精确定位内容容器，动态计算其实际高度，然后截取一张大小刚好的完整页面快照，并返回base64字符串。

    :param url: 要访问和截图的网页URL。
    :return: 截图的base64字符串
    """
    start_time = time.time()
    logger.info(f"开始截图流程，目标URL: {url}")

    screenshot_bytes = None

    async with async_playwright() as p:
        browser_start_time = time.time()
        browser = await p.chromium.launch(
            headless=False,  # 在无头服务器上运行时使用 xvfb-run
            slow_mo=500
        )
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = await context.new_page()
        browser_init_duration = time.time() - browser_start_time
        logger.info(f"浏览器初始化完成，耗时: {browser_init_duration:.2f}秒")

        try:
            navigation_start_time = time.time()
            logger.info(f"导航到: {url}")
            await page.goto(url, wait_until="networkidle", timeout=60000)
            navigation_duration = time.time() - navigation_start_time
            logger.info(f"页面初始加载完成，耗时: {navigation_duration:.2f}秒")

            dashboard_container_selector = ".react-grid-layout"
            logger.info(f"等待 Grafana 核心容器 '{dashboard_container_selector}' 渲染...")
            container_wait_start_time = time.time()
            try:
                await page.wait_for_selector(dashboard_container_selector, timeout=30000)
                container_wait_duration = time.time() - container_wait_start_time
                logger.info(f"检测到核心容器，耗时: {container_wait_duration:.2f}秒")
            except Exception as e:
                container_wait_duration = time.time() - container_wait_start_time
                logger.error(f"警告: 未能等到 Grafana 关键元素，耗时: {container_wait_duration:.2f}秒，错误: {e}")
                raise

            logger.info("设置临时超大视口以加载所有内容...")
            await page.set_viewport_size({'width': 1920, 'height': 12000})

            logger.info("等待网络和渲染稳定...")
            stability_start_time = time.time()
            await page.wait_for_load_state("networkidle", timeout=30000)
            await page.wait_for_timeout(5000)
            stability_duration = time.time() - stability_start_time
            logger.info(f"网络和渲染稳定完成，耗时: {stability_duration:.2f}秒")

            height_calc_start_time = time.time()
            js_code = f"document.querySelector('{dashboard_container_selector}').scrollHeight"
            actual_content_height = await page.evaluate(js_code)
            height_calc_duration = time.time() - height_calc_start_time
            logger.info(f"检测到内容容器的实际高度为: {actual_content_height}px，耗时: {height_calc_duration:.2f}秒")

            if actual_content_height <= 1080:
                logger.warning(f"计算出的内容高度({actual_content_height}px)可能不正确，使用默认高度8000px")
                actual_content_height = 8000

            logger.info("将视口调整为内容的实际高度...")
            viewport_adjust_start_time = time.time()
            final_height = actual_content_height + 100
            await page.set_viewport_size({'width': 1920, 'height': final_height})
            await page.wait_for_timeout(2000)
            viewport_adjust_duration = time.time() - viewport_adjust_start_time
            logger.info(f"视口调整完成，最终高度: {final_height}px，耗时: {viewport_adjust_duration:.2f}秒")

            logger.info("正在截取大小刚好的完整页面快照...")
            screenshot_start_time = time.time()
            screenshot_bytes = await page.screenshot(full_page=True)
            screenshot_duration = time.time() - screenshot_start_time
            logger.info(f"截图完成，耗时: {screenshot_duration:.2f}秒")
        except Exception as e:
            error_duration = time.time() - start_time
            logger.error(f"在操作过程中发生错误，总耗时: {error_duration:.2f}秒，错误: {e}")
            raise
        finally:
            cleanup_start_time = time.time()
            logger.info("关闭浏览器...")
            await page.close()
            await context.close()
            await browser.close()
            cleanup_duration = time.time() - cleanup_start_time
            logger.info(f"浏览器清理完成，耗时: {cleanup_duration:.2f}秒")

    if screenshot_bytes is None:
        total_duration = time.time() - start_time
        logger.error(f"截图失败，未获取到图片数据，总耗时: {total_duration:.2f}秒")
        raise RuntimeError("截图失败，未获取到图片数据。")

    encoding_start_time = time.time()
    base64_result = base64.b64encode(screenshot_bytes).decode("utf-8")
    encoding_duration = time.time() - encoding_start_time
    total_duration = time.time() - start_time

    logger.info(f"截图流程完成 - Base64编码耗时: {encoding_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
    return base64_result

if __name__ == "__main__":
    async def main():
        url = "https://cnp-proxy.gwm.cn/grafana/d/oZpynZ7mz/cnp-processes?orgId=1"
        base64_image = await get_grafana_processes_screenshot_base64(url)
        print(f"Base64 Encoded Image: {base64_image[:100]}...")
        with open("grafana_processes_screenshot_base64.txt", "w") as f:
            f.write("data:image/png;base64,"+base64_image)

        url = "https://cnp-proxy.gwm.cn/grafana/d/containers/cnp-pod?orgId=1&refresh=30s&var-ds=cnp-thanos&var-src=cnp&var-region=baoding&var-env=AI%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83&var-namespace=monitoring&var-container=alertmanager&var-pod=All"
        base64_image = await capture_full_page_screenshot_base64(url)
        print(f"Base64 Encoded Image: {base64_image[:100]}...")
        with open("capture_full_page_screenshot_base64.txt", "w") as f:
            f.write("data:image/png;base64,"+base64_image)
    
    asyncio.run(main())
