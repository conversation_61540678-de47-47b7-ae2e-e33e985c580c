from playwright.sync_api import sync_playwright
import time
import os

def capture_full_page_screenshot_and_video(url, screenshot_path="full_page_screenshot_with_video.png", video_dir="videos"):
    """
    启动浏览器，精确定位内容容器，动态计算其实际高度，然后截取一张大小刚好的完整页面快照，并录制操作视频。

    :param url: 要访问和截图的网页URL。
    :param screenshot_path: 完整页面截图的保存路径。
    :param video_dir: 操作过程视频的保存目录。
    """
    os.makedirs(video_dir, exist_ok=True)

    with sync_playwright() as p:
        browser = p.chromium.launch(
            headless=False,  # 确保在无头服务器上运行时使用 xvfb-run
            slow_mo=500
        )
        
        # 为了调试，临时增大录制视频的高度，以便能看到完整的视口变化
        context = browser.new_context(
            record_video_dir=video_dir,
            record_video_size={'width': 1920, 'height': 4000}, # 增加视频高度
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = context.new_page()

        try:
            print(f"导航到: {url}")
            page.goto(url, wait_until="networkidle", timeout=60000)
            print("页面初始加载完成。")

            # 等待 Grafana 的核心布局容器出现
            dashboard_container_selector = ".react-grid-layout"
            print(f"等待 Grafana 核心容器 '{dashboard_container_selector}' 渲染...")
            try:
                page.wait_for_selector(dashboard_container_selector, timeout=30000)
                print("检测到核心容器。")
            except Exception as e:
                print(f"警告: 未能等到 Grafana 关键元素: {e}。截图可能失败。")
                raise

            # 步骤 1: 设置一个足够大的临时视口以强制所有图表加载
            print("设置临时超大视口以加载所有内容...")
            page.set_viewport_size({'width': 1920, 'height': 12000})
            
            print("等待网络和渲染稳定...")
            page.wait_for_load_state("networkidle", timeout=30000)
            time.sleep(5)  # 额外等待，确保图表动画和渲染完成

            # 步骤 2: 精确计算核心内容容器的实际高度
            js_code = f"document.querySelector('{dashboard_container_selector}').scrollHeight"
            actual_content_height = page.evaluate(js_code)
            print(f"检测到内容容器的实际高度为: {actual_content_height}px")

            if actual_content_height <= 1080: # 如果高度不正常，则报错
                 print(f"错误：计算出的内容高度({actual_content_height}px)不正确，可能导致截图失败。")
                 actual_content_height = 8000 # 使用一个备用高度

            # 步骤 3: 将视口精确调整为内容的实际高度
            print("将视口调整为内容的实际高度...")
            final_height = actual_content_height + 100 # 增加一点缓冲区
            page.set_viewport_size({'width': 1920, 'height': final_height})
            time.sleep(2) # 等待视口调整生效

            print("正在截取大小刚好的完整页面快照...")
            page.screenshot(path=screenshot_path, full_page=True)
            print(f"成功截取完整页面截图，并保存为: {screenshot_path}")

        except Exception as e:
            print(f"在操作过程中发生错误: {e}")
        finally:
            print("关闭浏览器...")
            page.close()
            context.close()
            browser.close()
            print(f"操作完成。视频已保存至 '{video_dir}' 目录。")

if __name__ == "__main__":
    # target_url = "https://cnp-proxy.gwm.cn/grafana/d/s0rTx2PVz/cnp-node?orgId=1&var-ds=cnp-thanos&var-src=cnp&var-region=baoding&var-env=%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83&var-instance=10.245.16.10:9100"
    target_url = "https://cnp-proxy.gwm.cn/grafana/d/containers/cnp-pod?orgId=1&refresh=30s&var-ds=cnp-thanos&var-src=cnp&var-region=baoding&var-env=AI%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83&var-namespace=monitoring&var-container=alertmanager&var-pod=All"
    output_screenshot_name = "example_fullpage_pod_with_video.png"
    output_video_directory = "grafana_videos"
    
    capture_full_page_screenshot_and_video(target_url, output_screenshot_name, output_video_directory)


#     docker run -it --rm -v $(pwd):/app -p 8001 -w /app mcr.microsoft.com/playwright/python:v1.53.0-jammy bash

# pip install requests aiohttp FastMCP playwright==1.53.0 -i https://pypi.tuna.tsinghua.edu.cn/simple && xvfb-run python mcp_tools.py