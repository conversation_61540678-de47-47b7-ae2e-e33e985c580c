import requests
import time
from typing import List, Dict, Any, Optional

def get_node_info_by_ip(ip: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    根据传入的ip信息模糊匹配主机信息，返回env_zh, instance, region, src等字段。
    如果ip为空，则返回全量数据。
    """
    url = "https://cnp-proxy.gwm.cn/grafana/api/datasources/12/resources/api/v1/series"
    now = int(time.time())
    one_hour_ago = now - 3600
    data = {
        'match[]': 'node_exporter_build_info{}',
        'start': str(one_hour_ago),
        'end': str(now)
    }
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        response = requests.post(url, data=data, headers=headers, timeout=10)
        response.raise_for_status()
        result = response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return []
    if result.get('status') != 'success' or 'data' not in result:
        return []
    hosts = result['data']
    filtered = []
    seen = set()
    for host in hosts:
        instance = host.get('instance', '')
        key = (host.get('env_zh'), instance, host.get('region'), host.get('src'))
        if key in seen:
            continue
        seen.add(key)
        if ip:
            if ip in instance:
                filtered.append({
                    'env_zh': host.get('env_zh'),
                    'instance': instance,
                    'region': host.get('region'),
                    'src': host.get('src')
                })
        else:
            filtered.append({
                'env_zh': host.get('env_zh'),
                'instance': instance,
                'region': host.get('region'),
                'src': host.get('src')
            })
    return filtered

if __name__ == "__main__":
    # 示例：传入IP模糊匹配
    ip = ""  # 可替换为你要查询的IP，或设为None返回全量
    result = get_host_info_by_ip(ip)
    for item in result:
        print(item)
