from urllib.parse import urlencode, quote
import time
import logging
from datetime import datetime
from grafana_screenshot_base64 import capture_full_page_screenshot_base64
from grafana_screenshot_base64 import get_grafana_processes_screenshot_base64
import asyncio
import aiohttp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        # logging.FileHandler('grafana_monitor.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 全局LLM API配置
LLM_API_URL = "https://llmproxy.gwm.cn/v1/chat/completions"
LLM_API_KEY = "Bl1LdGr4vUKFl0mXoEKP///o4s+yV8JMoCC1YM/Y2bqF5LHdQAAN0HceKuFNns/r"

async def analyze_monitoring_image_with_llm(image_base64: str, content: str):
    """
    通用的监控图表分析函数，调用大模型分析监控图片

    :param image_base64: 监控图表的base64图片数据
    :param content: 系统提示内容
    :return: 大模型分析结果
    """
    start_time = time.time()
    logger.info("开始调用大模型分析监控图表")

    try:
        # 构造大模型请求
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        data = {
            "model": "default/qwen2-5-vl-7b-instruct-awq",
            "messages": [
                {
                    "role": "system",
                    "content": content
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_base64
                            }
                        }
                    ]
                }
            ]
        }

        logger.info(f"向LLM API发送请求: {LLM_API_URL}")
        api_start_time = time.time()

        # 调用大模型API
        async with aiohttp.ClientSession() as session:
            async with session.post(LLM_API_URL, headers=headers, json=data) as resp:
                resp.raise_for_status()
                result = await resp.json()

        api_duration = time.time() - api_start_time
        logger.info(f"LLM API调用完成，耗时: {api_duration:.2f}秒")

        # 解析并返回分析结果
        analysis_result = result["choices"][0]["message"]["content"]
        total_duration = time.time() - start_time
        logger.info(f"大模型分析完成，总耗时: {total_duration:.2f}秒")

        return analysis_result

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"大模型分析失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise

def pod_url(
    var_env,
    var_namespace,
    var_container,
    var_pod=None,
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """构建Pod监控的Grafana URL"""
    start_time = time.time()
    logger.info(f"构建Pod监控URL - 环境: {var_env}, 命名空间: {var_namespace}, 容器: {var_container}, Pod: {var_pod}")

    # 当前时间戳（毫秒）
    now = int(time.time() * 1000)
    # 默认最近一小时
    if to_time is None:
        to_time = now
    if from_time is None:
        from_time = now - 3600 * 1000

    base_url = "https://cnp-proxy.gwm.cn/grafana/d/containers/cnp-pod"
    params = {
        "var-ds": var_ds,
        "var-src": var_src,
        "var-region": var_region,
        "var-env": var_env,
        "var-namespace": var_namespace,
        "var-container": var_container,
        "var-pod": var_pod if var_pod else "All",
        "from": from_time,
        "to": to_time
    }
    query_string = urlencode(params, quote_via=quote, safe='')
    if kiosk:
        query_string += "&kiosk"
    # 固定orgId=1
    url = f"{base_url}?orgId=1&{query_string}"

    duration = time.time() - start_time
    logger.info(f"Pod监控URL构建完成，耗时: {duration:.3f}秒")
    return url

async def pod_image(
    var_env,
    var_namespace,
    var_container,
    var_pod=None,
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    获取指定 pod 监控页面的截图 base64 字符串。

    :param var_env: 环境名称（如 "AI生产环境"）
    :param var_namespace: K8S 命名空间
    :param var_container: 容器名
    :param var_pod: Pod 名称（可选，默认 All）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 截图的 base64 字符串，格式为 "data:image/png;base64,xxxx"
    """
    start_time = time.time()
    logger.info(f"开始获取Pod监控截图 - 环境: {var_env}, 命名空间: {var_namespace}, 容器: {var_container}")

    try:
        url = pod_url(
            var_env=var_env,
            var_namespace=var_namespace,
            var_container=var_container,
            var_pod=var_pod,
            from_time=from_time,
            to_time=to_time,
            kiosk=kiosk,
            var_ds=var_ds,
            var_src=var_src,
            var_region=var_region
        )

        logger.info(f"开始截图，URL: {url}")
        screenshot_start_time = time.time()

        base64_image = await capture_full_page_screenshot_base64(url)

        screenshot_duration = time.time() - screenshot_start_time
        total_duration = time.time() - start_time

        logger.info(f"Pod监控截图完成 - 截图耗时: {screenshot_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")

        return "data:image/png;base64," + base64_image

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"Pod监控截图失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise

async def pod_monitor_analysis(
    var_env,
    var_namespace,
    var_container,
    var_pod=None,
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    获取pod监控图表图片并调用大模型分析，返回分析结果。
    """
    start_time = time.time()
    logger.info(f"开始Pod监控分析 - 环境: {var_env}, 命名空间: {var_namespace}, 容器: {var_container}")

    try:
        # 1. 获取监控图表图片
        image_start_time = time.time()
        image_base64 = await pod_image(
            var_env=var_env,
            var_namespace=var_namespace,
            var_container=var_container,
            var_pod=var_pod,
            from_time=from_time,
            to_time=to_time,
            kiosk=kiosk,
            var_ds=var_ds,
            var_src=var_src,
            var_region=var_region
        )
        image_duration = time.time() - image_start_time
        logger.info(f"Pod监控图片获取完成，耗时: {image_duration:.2f}秒")

        # 2. 调用通用分析函数
        content = """你是一个资深的Kubernetes运维专家，请仔细分析用户上传的Pod监控图表。

请重点关注图表中的以下关键指标并进行分析：

**图表解读重点**：
- CPU Rate: Pod中各容器的CPU使用率变化趋势
- Memory Rate: 内存使用率和总内存使用量
- CPU Usage: CPU使用量的绝对值和变化模式  
- Total Memory Usage: 总内存使用量和增长趋势
- Network Traffic (Inbound/Outbound): 网络流入流出带宽
- Read/Write IOPS: 磁盘读写操作频率和性能

**详细分析维度**：

1. **CPU性能分析**：
   - 分析CPU Rate图表中的使用率峰值和平均水平
   - 识别CPU使用的周期性模式和异常波动
   - 评估是否存在CPU资源不足或过度分配
   - 检查CPU throttling风险和性能瓶颈

2. **内存使用分析**：
   - 观察Memory Rate的增长趋势和稳定性
   - 分析Total Memory Usage的绝对使用量
   - 识别内存泄漏迹象和OOM风险
   - 评估内存limits设置的合理性

3. **网络流量分析**：
   - 分析Network Traffic的流入流出模式
   - 识别网络使用的业务特征和异常流量
   - 评估网络带宽是否充足
   - 检查网络延迟和连接稳定性

4. **存储I/O分析**：
   - 观察Read/Write IOPS的读写模式
   - 识别I/O密集型操作和性能瓶颈
   - 评估存储性能是否满足业务需求
   - 分析I/O等待时间和队列深度

5. **容器健康状态**：
   - 分析各容器的资源使用差异
   - 识别异常重启或状态变化
   - 评估容器间的资源竞争情况
   - 检查资源配置的均衡性

**输出要求**：
- **健康评分**：优秀(90-100)/良好(70-89)/警告(50-69)/严重(0-49)，并详细说明评分依据
- **关键发现**：列出3-5个最重要的发现，包括具体数值和时间点
- **风险识别**：指出潜在的性能风险和资源瓶颈
- **优化建议**：提供具体的资源配置调整建议，包括CPU/内存limits和requests
- **告警建议**：推荐关键指标的告警阈值设置
- **kubectl命令**：提供可直接执行的资源调整命令

请基于图表中的实际数据进行分析，避免泛泛而谈，提供具体可行的建议。"""

        # 3. 调用大模型分析
        analysis_start_time = time.time()
        result = await analyze_monitoring_image_with_llm(image_base64, content)
        analysis_duration = time.time() - analysis_start_time

        total_duration = time.time() - start_time
        logger.info(f"Pod监控分析完成 - 分析耗时: {analysis_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")

        return result

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"Pod监控分析失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise


def node_url(
    var_env,
    var_instance,
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    构建 node 监控的 Grafana URL
    """
    start_time = time.time()
    logger.info(f"构建Node监控URL - 环境: {var_env}, 实例: {var_instance}")

    # 如果 var_instance 结尾不是 :9100，则自动补全
    if ":" in str(var_instance):
        ip, port = str(var_instance).rsplit(":", 1)
        if port != "9100":
            var_instance = f"{ip}:9100"
    else:
        var_instance = f"{var_instance}:9100"

    now = int(time.time() * 1000)
    if to_time is None:
        to_time = now
    if from_time is None:
        from_time = now - 3600 * 1000

    base_url = "https://cnp-proxy.gwm.cn/grafana/d/s0rTx2PVz/cnp-node"
    params = {
        "var-ds": var_ds,
        "var-src": var_src,
        "var-region": var_region,
        "var-env": var_env,
        "var-instance": var_instance,
        "from": from_time,
        "to": to_time
    }
    query_string = urlencode(params, quote_via=quote, safe='')
    if kiosk:
        query_string += "&kiosk"
    url = f"{base_url}?orgId=1&{query_string}"

    duration = time.time() - start_time
    logger.info(f"Node监控URL构建完成，耗时: {duration:.3f}秒")
    return url

async def node_image(
    var_env,
    var_instance,
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    获取指定 node 监控页面的截图 base64 字符串。

    :param var_env: 环境名称（如 "生产环境"）
    :param var_instance: 节点实例（如 "10.245.16.18:9100"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 截图的 base64 字符串，格式为 "data:image/png;base64,xxxx"
    """
    start_time = time.time()
    logger.info(f"开始获取Node监控截图 - 环境: {var_env}, 实例: {var_instance}")

    try:
        url = node_url(
            var_env=var_env,
            var_instance=var_instance,
            from_time=from_time,
            to_time=to_time,
            kiosk=kiosk,
            var_ds=var_ds,
            var_src=var_src,
            var_region=var_region
        )

        logger.info(f"开始截图，URL: {url}")
        screenshot_start_time = time.time()

        base64_image = await capture_full_page_screenshot_base64(url)

        screenshot_duration = time.time() - screenshot_start_time
        total_duration = time.time() - start_time

        logger.info(f"Node监控截图完成 - 截图耗时: {screenshot_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")

        return "data:image/png;base64," + base64_image

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"Node监控截图失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise

async def node_monitor_analysis(
    var_env,
    var_instance,
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    获取node监控图表图片并调用大模型分析，返回分析结果。
    """
    start_time = time.time()
    logger.info(f"开始Node监控分析 - 环境: {var_env}, 实例: {var_instance}")

    try:
        # 1. 获取监控图表图片
        image_start_time = time.time()
        image_base64 = await node_image(
            var_env=var_env,
            var_instance=var_instance,
            from_time=from_time,
            to_time=to_time,
            kiosk=kiosk,
            var_ds=var_ds,
            var_src=var_src,
            var_region=var_region
        )
        image_duration = time.time() - image_start_time
        logger.info(f"Node监控图片获取完成，耗时: {image_duration:.2f}秒")

        # 2. 调用通用分析函数
        content = """你是一个资深的Kubernetes运维专家，请仔细分析用户上传的Node节点监控图表。

请重点关注图表中的以下关键指标并进行分析：

**图表解读重点**：
- CPU使用率: 节点整体CPU使用情况和变化趋势
- 系统负载: Load Average 1m/5m/15m指标
- 内存使用: 总内存、已用内存、可用内存、缓存和缓冲区
- 磁盘空间: 各挂载点的使用率和剩余空间
- 磁盘I/O: 读写IOPS、带宽和I/O等待时间
- 网络流量: 网络接口的流入流出带宽
- 系统进程: 进程数量、线程数和文件描述符使用情况

**详细分析维度**：

1. **CPU和负载分析**：
   - 分析CPU使用率的峰值、平均值和变化趋势
   - 评估系统负载Load Average是否合理(通常应小于CPU核数)
   - 识别CPU使用的周期性模式和异常峰值
   - 检查是否存在CPU瓶颈和性能问题

2. **内存资源分析**：
   - 观察内存使用率和可用内存变化
   - 分析缓存(Cache)和缓冲区(Buffer)的使用情况
   - 检查Swap使用情况和内存压力
   - 评估内存是否充足，是否存在内存泄漏风险

3. **存储性能分析**：
   - 检查各挂载点的磁盘空间使用率
   - 分析磁盘I/O的读写性能和IOPS
   - 观察I/O等待时间和队列深度
   - 识别存储瓶颈和容量不足风险

4. **网络性能分析**：
   - 分析网络接口的流量模式和带宽使用
   - 检查网络传输的稳定性和异常流量
   - 评估网络性能是否满足业务需求
   - 识别网络瓶颈和连接问题

5. **系统健康状态**：
   - 检查系统运行时间和稳定性
   - 分析进程数量和线程数的变化
   - 观察文件描述符使用情况
   - 评估系统整体健康状况

**输出要求**：
- **节点健康评分**：优秀(90-100)/良好(70-89)/警告(50-69)/严重(0-49)，并详细说明评分依据
- **关键发现**：列出3-5个最重要的发现，包括具体数值和时间点
- **风险识别**：指出潜在的性能风险和资源瓶颈
- **优化建议**：提供具体的系统调优建议，包括内核参数、资源配置等
- **容量规划**：基于当前使用趋势提供扩容建议
- **告警建议**：推荐关键指标的告警阈值设置
- **系统命令**：提供可直接执行的系统优化和排查命令

请基于图表中的实际数据进行分析，避免泛泛而谈，提供具体可行的建议。"""

        # 3. 调用大模型分析
        analysis_start_time = time.time()
        result = await analyze_monitoring_image_with_llm(image_base64, content)
        analysis_duration = time.time() - analysis_start_time

        total_duration = time.time() - start_time
        logger.info(f"Node监控分析完成 - 分析耗时: {analysis_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")

        return result

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"Node监控分析失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise


def processes_url(
    var_env,
    var_host,
    var_processes="All",
    var_interval="5m",
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    构建 processes 监控的 Grafana URL
    :param var_env: 环境名称（如 "测试环境"）
    :param var_host: 主机（如 "************:9256"）
    :param var_processes: 进程名（默认 "All"）
    :param var_interval: 监控间隔（默认 "5m"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 构建好的URL字符串
    """
    start_time = time.time()
    logger.info(f"构建Processes监控URL - 环境: {var_env}, 主机: {var_host}, 进程: {var_processes}")

    # 处理var_host端口
    if ":" in str(var_host):
        ip, port = str(var_host).rsplit(":", 1)
        if port != "9256":
            var_host = f"{ip}:9256"
    else:
        var_host = f"{var_host}:9256"

    now = int(time.time() * 1000)
    if to_time is None:
        to_time = now
    if from_time is None:
        from_time = now - 3600 * 1000

    base_url = "https://cnp-proxy.gwm.cn/grafana/d/oZpynZ7mz/cnp-processes"
    params = {
        "var-ds": var_ds,
        "var-src": var_src,
        "var-region": var_region,
        "var-env": var_env,
        "var-host": var_host,
        "var-processes": var_processes,
        "var-interval": var_interval,
        "from": from_time,
        "to": to_time
    }
    query_string = urlencode(params, quote_via=quote, safe='')
    if kiosk:
        query_string += "&kiosk"
    url = f"{base_url}?orgId=1&{query_string}"

    duration = time.time() - start_time
    logger.info(f"Processes监控URL构建完成，耗时: {duration:.3f}秒")
    return url

async def processes_image(
    var_env,
    var_host,
    var_processes="All",
    var_interval="5m",
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    获取指定 processes 监控页面的截图 base64 字符串。

    :param var_env: 环境名称
    :param var_host: 主机（如 "************:9256"）
    :param var_processes: 进程名（默认 "All"）
    :param var_interval: 监控间隔（默认 "5m"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 截图的 base64 字符串，格式为 "data:image/png;base64,xxxx"
    """
    start_time = time.time()
    logger.info(f"开始获取Processes监控截图 - 环境: {var_env}, 主机: {var_host}, 进程: {var_processes}")

    try:
        url = processes_url(
            var_env=var_env,
            var_host=var_host,
            var_processes=var_processes,
            var_interval=var_interval,
            from_time=from_time,
            to_time=to_time,
            kiosk=kiosk,
            var_ds=var_ds,
            var_src=var_src,
            var_region=var_region
        )

        logger.info(f"开始截图，URL: {url}")
        screenshot_start_time = time.time()

        base64_image = await get_grafana_processes_screenshot_base64(url)

        screenshot_duration = time.time() - screenshot_start_time
        total_duration = time.time() - start_time

        logger.info(f"Processes监控截图完成 - 截图耗时: {screenshot_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")

        return "data:image/png;base64," + base64_image

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"Processes监控截图失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise

async def processes_monitor_analysis(
    var_env,
    var_host,
    var_processes="All",
    var_interval="5m",
    from_time=None,
    to_time=None,
    kiosk=True,
    var_ds="cnp-thanos",
    var_src="cnp",
    var_region="baoding"
):
    """
    获取processes监控图表图片并调用大模型分析，返回分析结果。

    :param var_env: 环境名称
    :param var_host: 主机（如 "************:9256"）
    :param var_processes: 进程名（默认 "All"）
    :param var_interval: 监控间隔（默认 "5m"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 大模型分析结果
    """
    start_time = time.time()
    logger.info(f"开始Processes监控分析 - 环境: {var_env}, 主机: {var_host}, 进程: {var_processes}")

    try:
        # 1. 获取监控图表图片
        image_start_time = time.time()
        image_base64 = await processes_image(
            var_env=var_env,
            var_host=var_host,
            var_processes=var_processes,
            var_interval=var_interval,
            from_time=from_time,
            to_time=to_time,
            kiosk=kiosk,
            var_ds=var_ds,
            var_src=var_src,
            var_region=var_region
        )
        image_duration = time.time() - image_start_time
        logger.info(f"Processes监控图片获取完成，耗时: {image_duration:.2f}秒")

        # 2. 调用通用分析函数
        content = """你是一个资深的系统运维专家，请仔细分析用户上传的进程监控图表。

请重点关注图表中的以下关键指标并进行分析：

**图表解读重点**：
- CPU使用率: 各进程的CPU使用率和变化趋势
- 内存使用: 各进程的内存占用量和增长模式
- 进程数量: 系统中运行的进程总数变化
- 进程状态: 运行、睡眠、僵尸等状态的进程分布
- 文件描述符: 各进程打开的文件描述符数量
- 网络连接: 进程的网络连接数和状态
- I/O操作: 进程的磁盘读写操作频率

**详细分析维度**：

1. **进程资源消耗分析**：
   - 识别CPU使用率最高的进程和异常峰值
   - 分析内存使用量大的进程和内存增长趋势
   - 观察进程资源使用的时间模式和周期性
   - 检查是否存在资源泄漏或异常消耗

2. **进程数量和状态分析**：
   - 分析进程总数的变化趋势和稳定性
   - 检查各种状态进程的分布是否正常
   - 识别僵尸进程、孤儿进程等异常状态
   - 评估进程创建和销毁的频率

3. **系统资源竞争分析**：
   - 识别资源争用激烈的进程
   - 分析进程间的相互影响和依赖关系
   - 检查文件描述符使用是否接近系统限制
   - 评估网络连接数是否合理

4. **进程性能和稳定性**：
   - 观察进程启动、运行、退出的模式
   - 识别频繁重启或异常退出的进程
   - 分析进程响应时间和处理能力
   - 检查进程的健康状态和可用性

5. **系统负载影响评估**：
   - 评估各进程对系统整体负载的贡献
   - 分析高负载时期的进程行为变化
   - 识别系统瓶颈和性能限制因素
   - 检查进程调度和优先级设置

**输出要求**：
- **进程健康评分**：优秀(90-100)/良好(70-89)/警告(50-69)/严重(0-49)，并详细说明评分依据
- **关键发现**：列出3-5个最重要的发现，包括具体进程名称、资源使用数值和时间点
- **异常进程识别**：指出资源使用异常、状态异常或行为异常的进程
- **性能瓶颈分析**：识别系统性能瓶颈和资源争用问题
- **优化建议**：提供具体的进程配置和系统调优建议
- **监控告警**：推荐关键进程和指标的告警阈值设置
- **运维命令**：提供可直接执行的进程管理和排查命令
- **预防措施**：提供进程管理和系统优化的最佳实践

请基于图表中的实际数据进行分析，重点关注具体的进程名称和数值，避免泛泛而谈，提供具体可行的建议。"""

        # 3. 调用大模型分析
        analysis_start_time = time.time()
        result = await analyze_monitoring_image_with_llm(image_base64, content)
        analysis_duration = time.time() - analysis_start_time

        total_duration = time.time() - start_time
        logger.info(f"Processes监控分析完成 - 分析耗时: {analysis_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")

        return result

    except Exception as e:
        error_duration = time.time() - start_time
        logger.error(f"Processes监控分析失败，耗时: {error_duration:.2f}秒，错误: {str(e)}")
        raise

# 示例调用
if __name__ == "__main__":
    async def main():
        # url = node_url(
        #     var_env="生产环境",
        #     var_instance="10.255.132.10:9100"
        # )
        # print(url)

        # # 示例调用
        # url = pod_url(
        #     var_env="AI生产环境",
        #     var_namespace="monitoring",
        #     var_container="alertmanager"
        # )
        # print(url)

        # analysis = await pod_monitor_analysis(
        #     var_env="AI生产环境",
        #     var_namespace="monitoring",
        #     var_container="alertmanager"
        # )
        # print(analysis)

        analysis = await node_monitor_analysis(
            var_env="AI生产环境",
            var_instance="10.255.132.10:9100"
        )
        print(analysis)

    asyncio.run(main())