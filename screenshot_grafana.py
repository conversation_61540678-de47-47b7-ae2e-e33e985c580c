from playwright.sync_api import sync_playwright
import time
import os

def scroll_to_bottom(page):
    """逐步滚动到页面底部，确保所有内容加载"""
    previous_height = 0
    while True:
        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        time.sleep(2)  # 等待内容加载

        current_height = page.evaluate("document.body.scrollHeight")
        print(f"[DEBUG] Scrolled to height: {current_height}")

        if current_height == previous_height:
            break
        previous_height = current_height

def capture_full_page_screenshot(page, path):
    """捕获整个页面的截图"""
    # 获取页面总高度
    total_height = page.evaluate("document.body.scrollHeight")
    print(f"[DEBUG] Total page height: {total_height}px")

    # 设置较大的视口高度
    page.set_viewport_size({"width": 1920, "height": total_height})

    # 滚动到顶部
    page.evaluate("window.scrollTo(0, 0)")

    # 截图整个页面
    page.screenshot(path=path, full_page=True)

def segment_screenshot(page, path_prefix):
    """分段截图并保存"""
    # 获取页面总高度
    total_height = page.evaluate("document.body.scrollHeight")
    print(f"[DEBUG] Total page height: {total_height}px")

    # 视口宽度
    viewport_width = page.viewport_size["width"]

    # 分段截图参数
    chunk_height = 1000  # 每次截图的高度
    num_chunks = (total_height + chunk_height - 1) // chunk_height  # 计算分段数量

    # 创建临时文件夹存放分段截图
    temp_dir = "temp_screenshots"
    os.makedirs(temp_dir, exist_ok=True)

    # 分段截图
    for i in range(num_chunks):
        # 滚动到当前分段的起始位置
        page.evaluate(f"window.scrollTo(0, {i * chunk_height})")
        time.sleep(1)

        # 截图当前分段
        screenshot_path = os.path.join(temp_dir, f"{path_prefix}_chunk_{i}.png")
        page.screenshot(path=screenshot_path, clip={"x": 0, "y": i * chunk_height, "width": viewport_width, "height": chunk_height})
        print(f"[DEBUG] Captured segment {i} at {screenshot_path}")

    # 合并分段截图（这里假设使用 ImageMagick 或其他工具合并）
    print(f"[DEBUG] Segments saved in {temp_dir}. Use an image merging tool to combine them.")

with sync_playwright() as p:
    browser = p.chromium.launch(headless=False)
    page = browser.new_page()

    # 设置初始视口大小
    viewport_size = {"width": 1920, "height": 5000}
    page.set_viewport_size(viewport_size)
    print(f"[DEBUG] Set viewport size: {viewport_size}")

    url = "https://cnp-proxy.gwm.cn/grafana/d/oZpynZ7mz/cnp-processes?orgId=1"
    print(f"[DEBUG] Navigating to URL: {url}")
    page.goto(url)

    # 等待页面基本加载完成
    print("[DEBUG] Waiting for network idle...")
    page.wait_for_load_state("networkidle")
    time.sleep(5)  # 初始等待确保页面加载

    # 展开所有面板
    toggle_buttons = page.query_selector_all("div[data-testid*='dashboard-row'] button")
    if not toggle_buttons:
        print("[DEBUG] No toggle buttons found.")
    else:
        print(f"[DEBUG] Found {len(toggle_buttons)} toggle buttons.")
        for i, button in enumerate(toggle_buttons):
            if i == 0:
                print("[DEBUG] Skipping first panel (index 0).")
                continue
            try:
                button.scroll_into_view_if_needed()
                button.wait_for_element_state("visible")
                button.click()
                print(f"[DEBUG] Expanded panel {i + 1}")
                page.wait_for_load_state("networkidle")
                time.sleep(3)
            except Exception as e:
                print(f"[ERROR] Failed to expand panel {i + 1}: {e}")

    # 滚动到底部，确保所有内容加载
    print("[DEBUG] Starting to scroll to bottom...")
    scroll_to_bottom(page)
    time.sleep(10)  # 确保最后的数据也加载完成

    # 方法一：使用 full_page=True 截图
    capture_full_page_screenshot(page, "full_page_screenshot_final.png")

    # 方法二：分段截图（备用方案）
    # segment_screenshot(page, "segment_screenshot")

    browser.close()