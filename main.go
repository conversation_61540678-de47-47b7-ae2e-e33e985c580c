package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/playwright-community/playwright-go"
)

func main1() {
	// 定义请求 URL
	requestURL := "https://cnp-proxy.gwm.cn/grafana/api/datasources/12/resources/api/v1/series"

	// 构建表单数据
	data := url.Values{}
	data.Set("match[]", "node_exporter_build_info{}")

	// 创建请求体
	// strings.NewReader(data.Encode()) 会将 url.Values 编码为 "key=value&key2=value2" 格式的字符串
	// 并作为请求体发送，Content-Type 会自动设置为 application/x-www-form-urlencoded
	req, err := http.NewRequest("POST", requestURL, strings.NewReader(data.Encode()))
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	// 设置 Content-Type 头，这对于 --data-urlencode 是必需的
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close() // 确保关闭响应体

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	// 打印响应状态码到标准错误，响应体到标准输出
	fmt.Fprintf(os.Stderr, "响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("%s\n", string(body))
}

func captureFullPageScreenshot(url, outputPath string) error {
	// 启动 Playwright
	pw, err := playwright.Run(&playwright.RunOptions{
		SkipInstallBrowsers: true, // 因为我们已经手动安装了浏览器
	})
	if err != nil {
		return fmt.Errorf("无法启动 Playwright: %w", err)
	}
	defer pw.Stop()

	// 启动 Chromium 浏览器
	browser, err := pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(true), // 设置为无头模式
	})
	if err != nil {
		return fmt.Errorf("无法启动浏览器: %w", err)
	}
	defer browser.Close()

	// 创建一个新页面
	page, err := browser.NewPage()
	if err != nil {
		return fmt.Errorf("无法创建页面: %w", err)
	}

	// 导航到 URL 并等待网络空闲，确保所有数据加载完毕
	// 'networkidle' 意味着在过去 500ms 内没有新的网络请求
	_, err = page.Goto(url, playwright.PageGotoOptions{WaitUntil: playwright.WaitUntilStateNetworkidle})
	if err != nil {
		return fmt.Errorf("无法导航到 URL %s: %w", url, err)
	}

	// 截取完整页面截图
	screenshotBytes, err := page.Screenshot(playwright.PageScreenshotOptions{
		FullPage: playwright.Bool(true), // 截取完整页面
	})
	if err != nil {
		return fmt.Errorf("无法截取屏幕截图: %w", err)
	}

	// 将截图保存到文件
	err = os.WriteFile(outputPath, screenshotBytes, 0644)
	if err != nil {
		return fmt.Errorf("无法保存截图到文件 %s: %w", outputPath, err)
	}

	fmt.Printf("成功截取 %s 的完整截图并保存为 %s\n", url, outputPath)
	return nil
}

func main() {
	targetURL := "https://www.google.com" // 将此替换为你想要截图的 URL
	outputFilename := "google_fullpage.png"

	if err := captureFullPageScreenshot(targetURL, outputFilename); err != nil {
		log.Fatalf("自动化截图失败: %v", err)
	}
}
