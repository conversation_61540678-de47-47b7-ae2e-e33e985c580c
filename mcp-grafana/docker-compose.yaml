services:
  grafana:
    image: grafana/grafana
    environment:
      GF_AUTH_ANONYMOUS_ENABLED: "true"
      GF_AUTH_ANONYMOUS_ORG_ROLE: "Admin"
      GF_LOG_LEVEL: debug
      GF_SERVER_ROUTER_LOGGING: "true"
    ports:
      - 3000:3000/tcp
    volumes:
      - ./testdata/provisioning:/etc/grafana/provisioning
      - ./testdata/dashboards:/var/lib/grafana/dashboards

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    entrypoint: /etc/prometheus/entrypoint.sh
    volumes:
      - ./testdata/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./testdata/prometheus-seed.yml:/etc/prometheus/prometheus-seed.yml
      - ./testdata/prometheus-entrypoint.sh:/etc/prometheus/entrypoint.sh

  loki:
    image: grafana/loki:2.6.0
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/loki-config.yml
    volumes:
      - ./testdata/loki-config.yml:/etc/loki/loki-config.yml

  promtail:
    image: grafana/promtail:2.6.0
    volumes:
      - ./testdata/promtail-config.yml:/etc/promtail/config.yml
      - /var/log:/var/log
      - /var/run/docker.sock:/var/run/docker.sock
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki

  pyroscope:
    image: grafana/pyroscope:1.13.4
    ports:
      - 4040:4040
