apiVersion: 1

datasources:
  - name: Prometheus
    id: 1
    uid: prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
  - name: Prometheus Demo
    id: 2
    uid: prometheus-demo
    type: prometheus
    access: proxy
    url: https://prometheus.demo.prometheus.io
  - name: Loki
    id: 3
    uid: loki
    type: loki
    access: proxy
    url: http://loki:3100
    isDefault: false
  - name: pyroscope
    uid: pyroscope
    type: grafana-pyroscope-datasource
    access: proxy
    url: http://pyroscope:4040
    isDefault: false
