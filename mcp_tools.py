from fastmcp import FastMCP
from get_node_info import get_node_info_by_ip
from get_pod_info import get_pod_info
from grafana_url import (
    pod_image as grafana_pod_image,
    node_image as grafana_node_image,
    processes_image as grafana_processes_image,
    pod_monitor_analysis as grafana_pod_monitor_analysis,
    node_monitor_analysis as grafana_node_monitor_analysis,
    processes_monitor_analysis as grafana_processes_monitor_analysis
)

mcp = FastMCP()


@mcp.tool
def pod_info(name: str | None = None):
    """
    获取应用服务信息，支持 业务组模糊匹配 或 应用模糊匹配。
    :param name: 需要模糊匹配的业务组或应用名称关键字，如果为None则返回全量数据。
    :return: 包含src, region, pod, namespace, env, container字段的pod信息列表。
    """
    return get_pod_info(name)

@mcp.tool
async def pod_image(
    var_env: str,
    var_namespace: str,
    var_container: str,
    var_pod: str | None = None,
    from_time: int | None = None,
    to_time: int | None = None,
    kiosk: bool = True,
    var_ds: str = "cnp-thanos",
    var_src: str = "cnp",
    var_region: str = "baoding"
):
    """
    获取指定 pod 监控页面的截图 base64 字符串。

    :param var_env: 环境名称（如 "AI生产环境"）
    :param var_namespace: K8S 命名空间
    :param var_container: 容器名
    :param var_pod: Pod 名称（可选，默认 All）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 截图的 base64 字符串，格式为 "data:image/png;base64,xxxx"
    """
    return await grafana_pod_image(
        var_env=var_env,
        var_namespace=var_namespace,
        var_container=var_container,
        var_pod=var_pod,
        from_time=from_time,
        to_time=to_time,
        kiosk=kiosk,
        var_ds=var_ds,
        var_src=var_src,
        var_region=var_region
    )

@mcp.tool
async def pod_monitor_analysis(
    var_env: str,
    var_namespace: str,
    var_container: str,
    var_pod: str | None = None,
    from_time: int | None = None,
    to_time: int | None = None,
    kiosk: bool = True,
    var_ds: str = "cnp-thanos",
    var_src: str = "cnp",
    var_region: str = "baoding"
):
    """
    获取pod监控图表图片并调用大模型分析，返回分析结果。

    :param var_env: 环境名称（如 "AI生产环境"）
    :param var_namespace: K8S 命名空间
    :param var_container: 容器名
    :param var_pod: Pod 名称（可选，默认 All）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 大模型分析结果
    """
    return await grafana_pod_monitor_analysis(
        var_env=var_env,
        var_namespace=var_namespace,
        var_container=var_container,
        var_pod=var_pod,
        from_time=from_time,
        to_time=to_time,
        kiosk=kiosk,
        var_ds=var_ds,
        var_src=var_src,
        var_region=var_region
    )

@mcp.tool
def node_info(ip: str | None = None):
    """
    根据传入的ip信息模糊匹配主机信息，返回env_zh, instance, region, src等字段。
    如果ip为空，则返回全量数据。
    """
    return get_node_info_by_ip(ip)

@mcp.tool
async def node_image(
    var_env: str,
    var_instance: str,
    from_time: int | None = None,
    to_time: int | None = None,
    kiosk: bool = True,
    var_ds: str = "cnp-thanos",
    var_src: str = "cnp",
    var_region: str = "baoding"
):
    """
    获取指定 node 监控页面的截图 base64 字符串。

    :param var_env: 环境名称（如 "生产环境"）
    :param var_instance: 节点实例（如 "************:9100"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 截图的 base64 字符串，格式为 "data:image/png;base64,xxxx"
    """
    return await grafana_node_image(
        var_env=var_env,
        var_instance=var_instance,
        from_time=from_time,
        to_time=to_time,
        kiosk=kiosk,
        var_ds=var_ds,
        var_src=var_src,
        var_region=var_region
    )

@mcp.tool
async def node_monitor_analysis(
    var_env: str,
    var_instance: str,
    from_time: int | None = None,
    to_time: int | None = None,
    kiosk: bool = True,
    var_ds: str = "cnp-thanos",
    var_src: str = "cnp",
    var_region: str = "baoding"
):
    """
    获取node监控图表图片并调用大模型分析，返回分析结果。

    :param var_env: 环境名称（如 "生产环境"）
    :param var_instance: 节点实例（如 "************:9100"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 大模型分析结果
    """
    return await grafana_node_monitor_analysis(
        var_env=var_env,
        var_instance=var_instance,
        from_time=from_time,
        to_time=to_time,
        kiosk=kiosk,
        var_ds=var_ds,
        var_src=var_src,
        var_region=var_region
    )

@mcp.tool
async def processes_image(
    var_env: str,
    var_host: str,
    var_processes: str = "All",
    var_interval: str = "5m",
    from_time: int | None = None,
    to_time: int | None = None,
    kiosk: bool = True,
    var_ds: str = "cnp-thanos",
    var_src: str = "cnp",
    var_region: str = "baoding"
):
    """
    获取指定 processes 监控页面的截图 base64 字符串。

    :param var_env: 环境名称
    :param var_host: 主机（如 "************:9256"）
    :param var_processes: 进程名（默认 "All"）
    :param var_interval: 监控间隔（默认 "5m"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 截图的 base64 字符串，格式为 "data:image/png;base64,xxxx"
    """
    return await grafana_processes_image(
        var_env=var_env,
        var_host=var_host,
        var_processes=var_processes,
        var_interval=var_interval,
        from_time=from_time,
        to_time=to_time,
        kiosk=kiosk,
        var_ds=var_ds,
        var_src=var_src,
        var_region=var_region
    )

@mcp.tool
async def processes_monitor_analysis(
    var_env: str,
    var_host: str,
    var_processes: str = "All",
    var_interval: str = "5m",
    from_time: int | None = None,
    to_time: int | None = None,
    kiosk: bool = True,
    var_ds: str = "cnp-thanos",
    var_src: str = "cnp",
    var_region: str = "baoding"
):
    """
    获取processes监控图表图片并调用大模型分析，返回分析结果。

    :param var_env: 环境名称
    :param var_host: 主机（如 "************:9256"）
    :param var_processes: 进程名（默认 "All"）
    :param var_interval: 监控间隔（默认 "5m"）
    :param from_time: 起始时间戳（毫秒，默认最近一小时）
    :param to_time: 结束时间戳（毫秒，默认当前时间）
    :param kiosk: 是否以 kiosk 模式展示（默认 True）
    :param var_ds: 数据源（默认 "cnp-thanos"）
    :param var_src: 来源（默认 "cnp"）
    :param var_region: 区域（默认 "baoding"）
    :return: 大模型分析结果
    """
    return await grafana_processes_monitor_analysis(
        var_env=var_env,
        var_host=var_host,
        var_processes=var_processes,
        var_interval=var_interval,
        from_time=from_time,
        to_time=to_time,
        kiosk=kiosk,
        var_ds=var_ds,
        var_src=var_src,
        var_region=var_region
    )

if __name__ == "__main__":
    mcp.run(transport="sse", host="0.0.0.0",port=8001)