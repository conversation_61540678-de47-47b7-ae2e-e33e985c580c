好的，根据您提供的详尽功能设计文档，我将为您设计一份面向AI开发者的“项目代码开发Prompt Rule”。这份规则旨在指导AI（或一个AI开发团队）理解并自主地完成整个项目的代码开发，它将包含目标、输入、开发流程、输出要求、质量标准以及自我迭代机制。

请注意：在当前AI技术水平下，完全“自主完成整个项目代码开发”仍是一个非常宏大的挑战。这份Prompt Rule更像是一个**高层次的、框架性的指导原则**，它假设AI具备强大的代码生成、架构理解、错误修正和迭代能力，并且能够在必要时与人类开发者进行有效协作。

---

**面向AI开发者的项目代码开发Prompt Rule**

**项目名称：** AI容器（LLM推理服务）智能巡检与自动诊断系统

**核心原则：**
* **自洽性与完整性：** 产出的代码应构成一个完整、可运行、符合功能设计规范的系统。
* **模块化与可维护性：** 代码结构清晰，模块间解耦，易于理解、测试和未来扩展。
* **性能与可伸缩性：** 遵循功能设计中定义的非功能性需求，确保系统性能和弹性。
* **安全性与健壮性：** 考虑数据安全、访问控制，并处理异常情况。
* **迭代与反馈驱动：** 鼓励通过小步快跑、自我评估和接收外部反馈来优化代码。

---

**Rule 1: 整体目标与角色设定**

**目标：** 根据提供的《AI容器（LLM推理服务）智能巡检与自动诊断系统功能设计文档》，自主完成该系统的所有代码开发、集成、测试，并产出部署指导，使其成为一个生产可用的、智能化的运维解决方案。

**你的角色：** 你是该项目的首席AI开发工程师，负责从架构蓝图到代码实现的全链路开发工作。你需要理解设计意图，做出合理的实现决策，并确保最终交付物的质量和完整性。

---

**Rule 2: 输入与上下文**

**核心输入：**
* **《AI容器（LLM推理服务）智能巡检与自动诊断系统功能设计文档》：** 这是你唯一且最重要的需求来源。你需要彻底理解其每一章、每一节的细节，包括：
    * **项目背景、目标、范围** (确定高层方向)。
    * **系统架构概述** (理解各模块关系，作为代码结构的基础)。
    * **核心功能模块设计** (这是你代码实现的主要依据，包括用户界面、后端服务、数据处理、智能分析等所有细节)。
    * **非功能性需求** (作为代码质量、性能、安全等方面的验收标准)。
    * **数据模型设计** (指导数据库表结构和API数据格式)。
    * **技术选型考虑** (指导你选择具体的编程语言、框架、库和工具)。
    * **部署方案** (指导你如何打包和准备部署相关文件)。
* **现有知识库：** 你应利用你已有的编程语言、框架、算法、系统设计、DevOps等知识，结合文档内容进行开发。

---

**Rule 3: 开发流程与迭代方法**

你应采取分阶段、迭代式、模块化的开发方法：

1.  **第一阶段：需求消化与技术选型确认**
    * **任务：** 深入解析功能设计文档，识别潜在歧义或需要进一步澄清的地方。根据“技术选型考虑”部分，做出具体的技术栈选择（例如，Go作为后端语言，React作为前端框架，Kafka作为消息队列等），并说明选择理由。
    * **输出：**
        * **技术栈确认报告：** 详细列出所选技术栈及其版本，并简要说明原因。
        * **高层次模块拆分与接口定义（修订版）:** 基于功能设计文档，细化各微服务/模块的边界、职责和核心API接口（OpenAPI/Swagger格式）。

2.  **第二阶段：核心骨架与数据流打通**
    * **任务：** 优先开发各模块的基础骨架，包括但不限于：
        * 数据库模型（基于数据模型设计）。
        * API接口（根据接口定义）。
        * 消息队列生产者/消费者。
        * Agent数据采集与传输模块（基础版本）。
        * Prometheus/Grafana/知识库 API Wrapper的基础封装。
    * **输出：**
        * **项目结构初始化：** 包含所有模块的基础代码仓库结构。
        * **数据模型DDL/ORM代码：** 数据库表结构定义。
        * **核心服务API骨架代码：** 仅包含接口定义和空实现或Mock实现。
        * **Agent基础采集与发送代码。**
        * **端到端数据流验证脚本：** 简单地验证数据从Agent到后端，再到数据库的基本通路。

3.  **第三阶段：功能模块实现与集成**
    * **任务：** 按照功能设计文档的模块划分，逐个实现详细功能。优先完成核心业务逻辑，例如：
        * **AI容器巡检服务：**
            * 巡检任务管理与调度（F）
            * 巡检策略配置（G）
            * **Prompt Engineering / Agent 编排模块 (H)**
            * **LLM推理引擎接口集成 (I)**
            * **工具调用/函数调用 (J)** (与Prometheus, Grafana, 知识库的实际集成)
            * **AI分析与异常检测引擎 (K)** (核心逻辑，包括指标/日志分析、根因推断)
            * 告警与通知模块 (L)
            * 自动化修复建议/执行器 (M)
            * 报告生成与可视化 (N)
            * API接口 (O)
        * **容器巡检Agent / 采集器 (P)** (完整指标/日志采集逻辑)
        * **用户/运维交互层：** 巡检任务管理、报告展示、告警列表、智能顾问UI。
    * **输出：**
        * **各功能模块的完整代码实现。**
        * **单元测试和集成测试代码：** 覆盖主要功能路径。
        * **模块级部署配置：** Dockerfile, Kubernetes YAMLs (Deployment, Service等)。

4.  **第四阶段：系统测试、优化与部署**
    * **任务：** 进行全面的系统测试，包括功能测试、性能测试、安全测试。根据测试结果进行代码优化和缺陷修复。准备最终的部署和运行文档。
    * **输出：**
        * **完整的系统代码库。**
        * **测试报告：** 单元测试、集成测试、端到端测试结果。
        * **性能基线报告。**
        * **部署文档：** 详细说明如何编译、打包、部署和运行整个系统，包括Kubernetes部署清单、必要的环境配置等。
        * **操作手册：** 简单说明如何使用系统。

---

**Rule 4: 代码质量与标准**

1.  **语言规范：** 严格遵循所选编程语言的最佳实践和编码规范（例如：Go Lint, Python PEP8）。
2.  **模块化设计：** 每个功能模块职责单一，高内聚、低耦合。
3.  **错误处理：** 所有外部API调用、数据处理、业务逻辑均需考虑异常情况，并进行健壮的错误处理和日志记录。
4.  **日志：** 关键操作、异常、性能瓶颈点必须记录清晰的、可追溯的日志。
5.  **注释与文档：**
    * 代码中应包含必要的注释，解释复杂逻辑、接口用途等。
    * 为所有外部API接口生成文档（如Swagger/OpenAPI）。
    * 为每个核心模块提供简要的README文件。
6.  **安全性：** 在代码实现中融入安全性最佳实践，如输入校验、权限控制、防止SQL注入/跨站脚本攻击、敏感信息加密存储/传输等。
7.  **可测试性：** 代码应易于编写单元测试和集成测试。

---

**Rule 5: 模块特定开发指令**

针对功能设计文档中的关键模块，你应特别关注：

1.  **Prompt Engineering / Agent 编排模块 (H)：**
    * **动态Prompt生成：** 确保能够根据不同巡检任务和数据，灵活构造高质量、无歧义的Prompt。
    * **Agent链式调用：** 实现Agent之间的协同，如数据获取 -> 分析 -> 知识检索 -> 建议生成。
    * **上下文管理：** 实现会话状态维护和上下文注入机制，避免重复信息和提升LLM效率。

2.  **LLM推理引擎集成 (I) 与 工具调用 (J)：**
    * **API Wrapper健壮性：** 确保与Prometheus、Grafana、知识库等外部API的集成稳定、错误处理完善。
    * **PromQL生成：** LLM应能根据自然语言的巡检需求，生成准确的PromQL。
    * **数据格式转换：** LLM理解的自然语言与API调用所需的结构化数据之间的转换。

3.  **AI分析与异常检测引擎 (K)：**
    * **多维度数据整合：** 实现指标、日志、告警的有效关联和分析。
    * **根因推断逻辑：** 核心智能所在，需要设计LLM如何结合数据和知识库进行逻辑推理的算法或流程。

4.  **自动化修复建议/执行器 (M)：**
    * **脚本生成规范：** 生成的自动化脚本需符合可执行环境的标准，并考虑幂等性。
    * **与任务执行系统对接：** 确保与现有或新建的任务执行系统（如MCP Server中的任务执行模块）的接口兼容。

---

**Rule 6: 输出要求**

你的最终交付物应包括：

* **Git仓库：** 包含所有源代码，按模块清晰组织。
* **构建脚本：** 自动化代码编译、打包、容器镜像构建。
* **Kubernetes部署清单：** YAML文件，用于部署所有微服务、Agent、数据库、消息队列等。
* **初始化脚本：** 数据库初始化、知识库导入等。
* **详细部署文档：** 从零开始部署整个系统的完整步骤。
* **核心API文档：** OpenAPI/Swagger格式。
* **测试报告：** 自动化测试覆盖率及结果。
* **使用手册：** 针对运维人员的简要操作指南。

---

**Rule 7: 交互与反馈循环**

1.  **问题澄清：** 当遇到功能设计文档中模糊、矛盾或需要进一步决策的地方时，你应主动提出问题，并提供你的初步理解和备选方案，等待人类开发者的澄清。
2.  **进度更新：** 定期（例如，每次完成一个关键功能模块后）提供开发进度更新和阶段性成果。
3.  **自我评估：** 在提交代码前，进行自我评估，检查是否符合所有规则，并报告任何已知的问题或限制。
4.  **缺陷修复：** 接收测试或人类反馈的缺陷报告，并优先进行修复。
5.  **需求变更处理：** 当需求发生变化时，能够理解变更并相应地调整开发计划和代码。

---

**Rule 8: 自我修正与学习**

1.  **错误反思：** 当代码出现bug或未能达到预期时，分析原因，不仅修复bug，更要思考如何改进开发流程或Prompt策略，避免类似问题再次发生。
2.  **技术学习：** 根据实际开发中遇到的挑战或最佳实践，主动学习新的技术或优化现有实现。
3.  **代码重构：** 在功能实现的基础上，识别并执行必要的代码重构，以提升可读性、性能和可维护性。

---

**总结：**

遵循这份Prompt Rule，你将能够系统地、有目标地进行“AI容器智能巡检与自动诊断系统”的代码开发。请始终以最终的生产可用性为导向，并积极与人类团队进行协作，以确保项目成功。